// 全局变量
let currentUser = null;
let isAdmin = false;
let usersLoaded = false; // 标记用户列表是否已加载
let productionLinesLoaded = false; // 标记生产线是否已加载
let devicesLoaded = false; // 标记设备列表是否已加载
let alarmInfoLoaded = false; // 标记故障信息是否已加载
let currentEditingAlarm = null; // 当前正在编辑的故障信息

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

// 初始化页面
function initializePage() {
    // 获取当前用户信息
    getCurrentUser();

    // 初始化事件监听器
    initializeEventListeners();
}

// 获取当前用户信息
function getCurrentUser() {
    // 从页面元素获取用户信息（这里简化处理，实际应该从服务器获取）
    const userElement = document.getElementById('current-user');
    if (userElement) {
        // 通过AJAX获取用户信息
        fetch('/api/admin/current-user')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentUser = data.user;
                    isAdmin = data.is_admin;
                    userElement.textContent = currentUser;

                    // 根据权限显示/隐藏管理员功能
                    if (isAdmin) {
                        document.body.classList.add('is-admin');
                    }
                }
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
            });
    }
}

// 初始化事件监听器
function initializeEventListeners() {
    // 修改密码表单
    const changePasswordForm = document.getElementById('change-password-form');
    if (changePasswordForm) {
        changePasswordForm.addEventListener('submit', handleChangePassword);
    }

    // 添加用户表单
    const addUserForm = document.getElementById('add-user-form');
    if (addUserForm) {
        addUserForm.addEventListener('submit', handleAddUser);
    }

    // 初始化故障信息表单事件监听器
    initializeAlarmFormListeners();
}

// 显示指定的内容区域
function showSection(sectionName) {
    // 隐藏所有内容区域
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });

    // 显示指定的内容区域
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // 更新导航菜单状态
    const menuItems = document.querySelectorAll('.sidebar-menu a');
    menuItems.forEach(item => {
        item.classList.remove('active');
    });

    // 设置当前菜单项为活动状态
    const activeMenuItem = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (activeMenuItem) {
        activeMenuItem.classList.add('active');
    }

    // 更新页面标题
    const titles = {
        'dashboard': '仪表盘',
        'users': '用户管理',
        'order-entry': '订单录入',
        'order-query': '订单查询',
        'production-export': '加工信息导出',
        'alarm-export': '报警信息导出',
        'tool-use': '刀具使用信息',
        'alarm-info': '故障信息管理',
        'export': '数据导出',
        'profile': '个人设置'
    };

    const pageTitle = document.getElementById('page-title');
    if (pageTitle && titles[sectionName]) {
        pageTitle.textContent = titles[sectionName];
    }

    // 如果是用户管理页面，加载用户列表（只加载一次）
    if (sectionName === 'users' && isAdmin && !usersLoaded) {
        loadUsers();
        usersLoaded = true;
    }

    // 如果是导出页面，加载生产线列表和设备列表（只加载一次）
    if ((sectionName === 'production-export' || sectionName === 'alarm-export') && !productionLinesLoaded) {
        loadProductionLines();
        productionLinesLoaded = true;
    }

    // 加载设备列表
    if ((sectionName === 'production-export' || sectionName === 'alarm-export') && !devicesLoaded) {
        loadDevices();
        devicesLoaded = true;
    }

    // 如果是刀具信息页面，加载生产线和设备列表
    if (sectionName === 'tool-use' && !toolLinesLoaded) {
        loadToolProductionLines();
        toolLinesLoaded = true;
    }

    if (sectionName === 'tool-use' && !toolDevicesLoaded) {
        loadToolDevices();
        toolDevicesLoaded = true;
    }

    // 如果是订单录入页面，初始化表单
    if (sectionName === 'order-entry') {
        initOrderEntryForm();
    }

    // 如果是订单查询页面，设置默认日期
    if (sectionName === 'order-query') {
        initOrderQueryForm();
    }

    // 如果是故障信息管理页面，加载故障信息列表
    if (sectionName === 'alarm-info' && isAdmin && !alarmInfoLoaded) {
        loadAlarmInfo();
        alarmInfoLoaded = true;
    }
}

// 加载用户列表
async function loadUsers() {
    try {
        const response = await fetch('/api/admin/users');
        const result = await response.json();

        if (result.success) {
            displayUsers(result.users);
        } else {
            showMessage('加载用户列表失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('加载用户列表失败: ' + error.message, 'error');
    }
}

// 显示用户列表
function displayUsers(users) {
    const tbody = document.querySelector('#users-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.username}</td>
            <td>${user.isadmin === 'Y' ? '管理员' : '普通用户'}</td>
            <td>${user.createtime}</td>
            <td>
                <button onclick="resetUserPassword('${user.dataid}')" class="btn btn-primary btn-small">重置密码</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 显示添加用户模态框
function showAddUserModal() {
    const modal = document.getElementById('add-user-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

// 关闭添加用户模态框
function closeAddUserModal() {
    const modal = document.getElementById('add-user-modal');
    if (modal) {
        modal.style.display = 'none';
        // 清空表单
        const form = document.getElementById('add-user-form');
        if (form) {
            form.reset();
        }
    }
}

// 处理添加用户
async function handleAddUser(event) {
    event.preventDefault();

    const username = document.getElementById('new-username').value;
    const password = document.getElementById('new-user-password').value;
    const isAdmin = document.getElementById('new-user-admin').value;

    try {
        const response = await fetch('/api/admin/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password,
                is_admin: isAdmin
            })
        });

        const result = await response.json();

        if (result.success) {
            showMessage(result.message, 'success');
            closeAddUserModal();
            usersLoaded = false; // 重置标记
            loadUsers(); // 重新加载用户列表
            usersLoaded = true; // 重新设置标记
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        showMessage('添加用户失败: ' + error.message, 'error');
    }
}

// 重置用户密码
async function resetUserPassword(userId) {
    const newPassword = prompt('请输入新密码:');
    if (!newPassword) return;

    try {
        const response = await fetch(`/api/admin/users/${userId}/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                new_password: newPassword
            })
        });

        const result = await response.json();

        if (result.success) {
            showMessage(result.message, 'success');
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        showMessage('重置密码失败: ' + error.message, 'error');
    }
}

// 处理修改密码
async function handleChangePassword(event) {
    event.preventDefault();

    const oldPassword = document.getElementById('old-password').value;
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;

    if (newPassword !== confirmPassword) {
        showMessage('新密码和确认密码不匹配', 'error');
        return;
    }

    try {
        const response = await fetch('/api/admin/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                old_password: oldPassword,
                new_password: newPassword
            })
        });

        const result = await response.json();

        if (result.success) {
            showMessage(result.message, 'success');
            // 清空表单
            document.getElementById('change-password-form').reset();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        showMessage('修改密码失败: ' + error.message, 'error');
    }
}

// 导出数据
async function exportData() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;

    try {
        const params = new URLSearchParams();
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);

        const response = await fetch(`/api/admin/export/production-data?${params}`);
        const result = await response.json();

        if (result.success) {
            displayExportData(result.data);
            showMessage('数据导出成功', 'success');
        } else {
            showMessage('导出失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('导出失败: ' + error.message, 'error');
    }
}

// 显示导出的数据
function displayExportData(data) {
    const resultDiv = document.getElementById('export-result');
    if (!resultDiv) return;

    if (data.length === 0) {
        resultDiv.innerHTML = '<p>没有找到数据</p>';
        return;
    }

    let html = `
        <table>
            <thead>
                <tr>
                    <th>设备号</th>
                    <th>设备名称</th>
                    <th>生产日期</th>
                    <th>班次</th>
                    <th>产量</th>
                    <th>创建时间</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.forEach(item => {
        html += `
            <tr>
                <td>${item.devno}</td>
                <td>${item.devname}</td>
                <td>${item.productdate}</td>
                <td>${item.workclass}</td>
                <td>${item.productcount}</td>
                <td>${item.createtime}</td>
            </tr>
        `;
    });

    html += '</tbody></table>';

    // 添加下载按钮
    html += `
        <div style="padding: 20px; text-align: center;">
            <button onclick="downloadCSV()" class="btn btn-primary">下载CSV文件</button>
        </div>
    `;

    resultDiv.innerHTML = html;

    // 保存数据供下载使用
    window.exportedData = data;
}

// 下载CSV文件
function downloadCSV() {
    if (!window.exportedData) return;

    const headers = ['设备号', '设备名称', '生产日期', '班次', '产量', '创建时间'];
    
    const csvRows = [
        headers.join(','),
        ...window.exportedData.map(item => [
            cleanCSVField(item.devno),
            cleanCSVField(item.devname),
            cleanCSVField(item.productdate),
            cleanCSVField(item.workclass),
            cleanCSVField(item.productcount),
            cleanCSVField(item.createtime)
        ].join(','))
    ];

    const csvContent = csvRows.join('\n');
    downloadFile(csvContent, `生产数据_${new Date().toISOString().split('T')[0]}.csv`);
}

// 下载文件通用函数
function downloadFile(content, filename) {
    // 添加BOM以确保Excel正确显示中文
    const bom = '\ufeff';
    const blob = new Blob([bom + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 显示消息
function showMessage(message, type) {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.position = 'fixed';
    messageDiv.style.top = '20px';
    messageDiv.style.right = '20px';
    messageDiv.style.zIndex = '9999';
    messageDiv.style.padding = '15px 20px';
    messageDiv.style.borderRadius = '5px';
    messageDiv.style.maxWidth = '300px';

    document.body.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// 子菜单切换功能
function toggleSubmenu(menuName) {
    const submenu = document.getElementById(menuName + '-submenu');
    const menuItem = submenu ? submenu.parentElement : null;

    if (!submenu || !menuItem) {
        console.warn(`找不到子菜单: ${menuName}`);
        return;
    }

    if (submenu.classList.contains('active')) {
        submenu.classList.remove('active');
        menuItem.classList.remove('expanded');
    } else {
        // 关闭其他子菜单
        document.querySelectorAll('.submenu').forEach(sub => {
            sub.classList.remove('active');
            if (sub.parentElement) {
                sub.parentElement.classList.remove('expanded');
            }
        });

        submenu.classList.add('active');
        menuItem.classList.add('expanded');
    }
}

// 加载生产线列表
async function loadProductionLines() {
    try {
        const response = await fetch('/api/admin/production-lines');
        const result = await response.json();

        if (result.success) {
            const prodLineSelect = document.getElementById('prod-line-select');
            const alarmLineSelect = document.getElementById('alarm-line-select');

            if (prodLineSelect) {
                result.lines.forEach(line => {
                    const option = document.createElement('option');
                    option.value = line.lineid;
                    option.textContent = line.linename;
                    prodLineSelect.appendChild(option);
                });

                // 添加生产线变化事件监听
                prodLineSelect.addEventListener('change', function() {
                    loadDevices(this.value, 'prod-device-select');
                });
            }

            if (alarmLineSelect) {
                result.lines.forEach(line => {
                    const option = document.createElement('option');
                    option.value = line.lineid;
                    option.textContent = line.linename;
                    alarmLineSelect.appendChild(option);
                });

                // 添加生产线变化事件监听
                alarmLineSelect.addEventListener('change', function() {
                    loadDevices(this.value, 'alarm-device-select');
                });
            }
        }
    } catch (error) {
        console.error('加载生产线列表失败:', error);
    }
}

// 加载设备列表
async function loadDevices(lineId = '', targetSelectId = '') {
    try {
        const url = lineId ? `/api/admin/devices?line_id=${lineId}` : '/api/admin/devices';
        const response = await fetch(url);
        const result = await response.json();

        if (result.success) {
            // 如果指定了目标下拉框，只更新该下拉框
            if (targetSelectId) {
                const deviceSelect = document.getElementById(targetSelectId);
                if (deviceSelect) {
                    // 清空现有选项（保留"全部设备"选项）
                    deviceSelect.innerHTML = '<option value="">全部设备</option>';

                    result.devices.forEach(device => {
                        const option = document.createElement('option');
                        option.value = device.devno;
                        option.textContent = `${device.devno} - ${device.devname}`;
                        deviceSelect.appendChild(option);
                    });
                }
            } else {
                // 更新所有设备下拉框
                const prodDeviceSelect = document.getElementById('prod-device-select');
                const alarmDeviceSelect = document.getElementById('alarm-device-select');

                [prodDeviceSelect, alarmDeviceSelect].forEach(deviceSelect => {
                    if (deviceSelect) {
                        result.devices.forEach(device => {
                            const option = document.createElement('option');
                            option.value = device.devno;
                            option.textContent = `${device.devno} - ${device.devname}`;
                            deviceSelect.appendChild(option);
                        });
                    }
                });
            }
        }
    } catch (error) {
        console.error('加载设备列表失败:', error);
    }
}

// 导出加工信息数据
async function exportProductionData() {
    const deviceSearch = document.getElementById('prod-device-search').value;
    const deviceNo = document.getElementById('prod-device-select').value;
    const lineId = document.getElementById('prod-line-select').value;
    const startDate = document.getElementById('prod-start-date').value;
    const endDate = document.getElementById('prod-end-date').value;
    const shift = document.getElementById('prod-shift-select').value;

    try {
        const params = new URLSearchParams();
        if (deviceSearch) params.append('device_search', deviceSearch);
        if (deviceNo) params.append('device_no', deviceNo);
        if (lineId) params.append('line_id', lineId);
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        if (shift) params.append('shift', shift);

        const response = await fetch(`/api/admin/export/production-records?${params}`);
        const result = await response.json();

        if (result.success) {
            displayProductionData(result.data);
            showMessage('加工信息导出成功', 'success');
        } else {
            showMessage('导出失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('导出失败: ' + error.message, 'error');
    }
}

// 导出报警信息数据
async function exportAlarmData() {
    const deviceSearch = document.getElementById('alarm-device-search').value;
    const deviceNo = document.getElementById('alarm-device-select').value;
    const lineId = document.getElementById('alarm-line-select').value;
    const startDate = document.getElementById('alarm-start-date').value;
    const endDate = document.getElementById('alarm-end-date').value;
    const alarmContent = document.getElementById('alarm-content-search').value;
    const durationMin = document.getElementById('alarm-duration-min').value;
    const durationMax = document.getElementById('alarm-duration-max').value;

    try {
        const params = new URLSearchParams();
        if (deviceSearch) params.append('device_search', deviceSearch);
        if (deviceNo) params.append('device_no', deviceNo);
        if (lineId) params.append('line_id', lineId);
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        if (alarmContent) params.append('alarm_content', alarmContent);
        if (durationMin) params.append('duration_min', durationMin);
        if (durationMax) params.append('duration_max', durationMax);

        const response = await fetch(`/api/admin/export/alarm-records?${params}`);
        const result = await response.json();

        if (result.success) {
            displayAlarmData(result.data);
            showMessage('报警信息导出成功', 'success');
        } else {
            showMessage('导出失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('导出失败: ' + error.message, 'error');
    }
}

// 显示加工信息数据（白班夜班分列显示）
function displayProductionData(data) {
    const resultDiv = document.getElementById('production-export-result');
    if (!resultDiv) return;

    if (data.length === 0) {
        resultDiv.innerHTML = '<p>没有找到符合条件的加工信息</p>';
        return;
    }

    let html = `
        <table>
            <thead>
                <tr>
                    <th>设备号</th>
                    <th>设备名称</th>
                    <th>生产线</th>
                    <th>生产日期</th>
                    <th>白班产量</th>
                    <th>夜班产量</th>
                    <th>日总产量</th>
                    <th>完成率(%)</th>
                    <th>设备OEE(%)</th>
                    <th>当前OEE</th>
                    <th>加工详情</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.forEach(item => {
        html += `
            <tr>
                <td>${item.devno}</td>
                <td>${item.devname}</td>
                <td>${item.linename}</td>
                <td>${item.productdate}</td>
                <td>${item.day_shift_count}</td>
                <td>${item.night_shift_count}</td>
                <td>${item.total_daily_count}</td>
                <td>${item.avg_finishrate}</td>
                <td>${item.avg_devoee}</td>
                <td>${item.avg_poweronrate}</td>
                <td>${item.productdetail || ''}</td>
            </tr>
        `;
    });

    html += '</tbody></table>';

    resultDiv.innerHTML = html;

    // 保存数据供下载使用
    window.productionExportData = data;

    // 显示下载按钮
    const downloadBtn = document.getElementById('download-production-btn');
    if (downloadBtn) {
        downloadBtn.style.display = 'inline-block';
    }
}

// 显示报警信息数据
function displayAlarmData(data) {
    const resultDiv = document.getElementById('alarm-export-result');
    if (!resultDiv) return;

    if (data.length === 0) {
        resultDiv.innerHTML = '<p>没有找到符合条件的报警信息</p>';
        return;
    }

    let html = `
        <table>
            <thead>
                <tr>
                    <th style="min-width: 100px;">设备编号</th>
                    <th style="min-width: 120px;">设备名称</th>
                    <th style="min-width: 100px;">所属生产线</th>
                    <th style="min-width: 200px;">报警详细内容</th>
                    <th style="min-width: 120px;">持续时长(分钟)</th>
                    <th style="min-width: 140px;">开始时间</th>
                    <th style="min-width: 140px;">结束时间</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.forEach(item => {
        html += `
            <tr>
                <td>${item.devno}</td>
                <td>${item.devname}</td>
                <td>${item.linename}</td>
                <td>${item.alarmcontent}</td>
                <td>${item.alarmtime}</td>
                <td>${item.createtime}</td>
                <td>${item.endtime || ''}</td>
            </tr>
        `;
    });

    html += '</tbody></table>';

    // 添加导出提示说明
    html += `
        <div style="padding: 20px; text-align: center; border-top: 1px solid #ddd; margin-top: 20px;">
            <div style="font-size: 12px; color: #666;">
                <p>💡 导出提示：如Excel中显示"#####"，请双击列边界自动调整列宽</p>
                <p>📅 导出格式：CSV文件（可用Excel直接打开）</p>
            </div>
        </div>
    `;

    resultDiv.innerHTML = html;

    // 保存数据供下载使用
    window.alarmExportData = data;

    // 显示下载按钮
    const downloadBtn = document.getElementById('download-alarm-btn');
    if (downloadBtn) {
        downloadBtn.style.display = 'inline-block';
    }
}

// 清空加工信息筛选条件
function clearProductionFilters() {
    document.getElementById('prod-device-search').value = '';
    document.getElementById('prod-device-select').value = '';
    document.getElementById('prod-line-select').value = '';
    document.getElementById('prod-start-date').value = '';
    document.getElementById('prod-end-date').value = '';
    document.getElementById('prod-shift-select').value = '';
    document.getElementById('production-export-result').innerHTML = '';

    // 隐藏下载按钮
    const downloadBtn = document.getElementById('download-production-btn');
    if (downloadBtn) {
        downloadBtn.style.display = 'none';
    }

    // 重新加载所有设备
    loadDevices('', 'prod-device-select');
}

// 清空报警信息筛选条件
function clearAlarmFilters() {
    document.getElementById('alarm-device-search').value = '';
    document.getElementById('alarm-device-select').value = '';
    document.getElementById('alarm-line-select').value = '';
    document.getElementById('alarm-start-date').value = '';
    document.getElementById('alarm-end-date').value = '';
    document.getElementById('alarm-content-search').value = '';
    document.getElementById('alarm-duration-min').value = '';
    document.getElementById('alarm-duration-max').value = '';
    document.getElementById('alarm-export-result').innerHTML = '';

    // 隐藏下载按钮
    const downloadBtn = document.getElementById('download-alarm-btn');
    if (downloadBtn) {
        downloadBtn.style.display = 'none';
    }

    // 重新加载所有设备
    loadDevices('', 'alarm-device-select');
}

// 清理CSV字段值，处理特殊字符
function cleanCSVField(value) {
    if (value == null || value === undefined) {
        return '';
    }
    
    // 转换为字符串并清理
    let str = String(value).trim();
    
    // 替换换行符、制表符和其他控制字符
    str = str.replace(/[\r\n\t]/g, ' ').replace(/\s+/g, ' ');
    
    // 移除可能导致Excel显示问题的特殊字符
    str = str.replace(/[^\x20-\x7E\u4e00-\u9fa5]/g, '');
    
    // 如果包含逗号、双引号、换行符或分号，需要用双引号包围
    if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r') || str.includes(';')) {
        // 转义内部的双引号
        str = str.replace(/"/g, '""');
        // 用双引号包围
        str = `"${str}"`;
    }
    
    // 对于长数字字符串，添加引号防止Excel科学计数法显示
    if (/^\d+\.?\d*$/.test(str) && str.length > 10) {
        str = `"${str}"`;
    }
    
    return str;
}

// 下载加工信息Excel文件（白班夜班分列）
function downloadProductionExcel() {
    if (!window.productionExportData) return;

    const headers = [
        '设备号', '设备名称', '生产线', '生产日期', '白班产量', '夜班产量', '日总产量',
        '完成率(%)', '设备OEE(%)', '当前OEE', '加工详情'
    ];

    // 使用cleanCSVField处理每个字段
    const csvRows = [
        headers.join(','),
        ...window.productionExportData.map(item => [
            cleanCSVField(item.devno),
            cleanCSVField(item.devname),
            cleanCSVField(item.linename),
            cleanCSVField(item.productdate),
            cleanCSVField(item.day_shift_count),
            cleanCSVField(item.night_shift_count),
            cleanCSVField(item.total_daily_count),
            cleanCSVField(item.avg_finishrate),
            cleanCSVField(item.avg_devoee),
            cleanCSVField(item.avg_poweronrate),
            cleanCSVField(item.productdetail)
        ].join(','))
    ];

    const csvContent = csvRows.join('\n');
    downloadFile(csvContent, `加工信息_${new Date().toISOString().split('T')[0]}.csv`);
}

// 下载报警信息Excel文件
function downloadAlarmExcel() {
    if (!window.alarmExportData) return;

    const headers = ['设备编号', '设备名称', '所属生产线', '报警详细内容', '持续时长(分钟)', '开始时间', '结束时间'];

    // 使用cleanCSVField处理每个字段
    const csvRows = [
        headers.join(','),
        ...window.alarmExportData.map(item => [
            cleanCSVField(item.devno),
            cleanCSVField(item.devname),
            cleanCSVField(item.linename),
            cleanCSVField(item.alarmcontent),
            cleanCSVField(item.alarmtime),
            cleanCSVField(item.createtime),
            cleanCSVField(item.endtime || '')
        ].join(','))
    ];

    const csvContent = csvRows.join('\n');
    
    // 生成更专业的文件名
    const now = new Date();
    const dateStr = now.getFullYear() + '年' + 
        String(now.getMonth() + 1).padStart(2, '0') + '月' + 
        String(now.getDate()).padStart(2, '0') + '日';
    const timeStr = String(now.getHours()).padStart(2, '0') + 
        String(now.getMinutes()).padStart(2, '0') + 
        String(now.getSeconds()).padStart(2, '0');
    const filename = `报警信息导出_${dateStr}_${timeStr}.csv`;
    
    downloadFile(csvContent, filename);
}

// 点击模态框外部关闭模态框
window.addEventListener('click', function(event) {
    const modal = document.getElementById('add-user-modal');
    if (event.target === modal) {
        closeAddUserModal();
    }
});

// 刀具信息相关功能
let toolLinesLoaded = false;
let toolDevicesLoaded = false;

// 加载刀具使用数据
async function loadToolUseData() {
    try {
        const lineId = document.getElementById('tool-line-select').value;
        const deviceId = document.getElementById('tool-device-select').value;
        const programName = document.getElementById('tool-program-name').value;

        const params = new URLSearchParams();
        if (lineId) params.append('line_id', lineId);
        if (deviceId) params.append('devno', deviceId);
        if (programName) params.append('program_name', programName);

        // 显示加载状态
        const resultDiv = document.getElementById('tool-use-result');
        if (resultDiv) {
            resultDiv.innerHTML = '<div class="loading">正在加载刀具数据...</div>';
        }

        const response = await fetch(`/api/admin/tool-use-data?${params}`);
        const result = await response.json();

        if (result.success) {
            displayToolUseData(result.data);
        } else {
            resultDiv.innerHTML = `<div class="error">加载失败: ${result.error}</div>`;
        }
    } catch (error) {
        console.error('加载刀具数据错误:', error);
        const resultDiv = document.getElementById('tool-use-result');
        if (resultDiv) {
            resultDiv.innerHTML = '<div class="error">加载失败，请重试</div>';
        }
    }
}

// 显示刀具使用数据
function displayToolUseData(data) {
    const resultDiv = document.getElementById('tool-use-result');
    if (!resultDiv) return;

    if (!data || data.length === 0) {
        resultDiv.innerHTML = '<div class="no-data">没有找到匹配的刀具数据</div>';
        return;
    }

    // 创建表格
    let tableHTML = `
        <div class="data-table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>设备编号</th>
                        <th>设备名称</th>
                        <th>刀具号</th>
                        <th>使用次数</th>
                        <th>程序名称</th>
                        <th>运行时间(分钟)</th>
                    </tr>
                </thead>
                <tbody>
    `;

    data.forEach(item => {
        tableHTML += `
            <tr>
                <td>${item.deviceNo || '-'}</td>
                <td>${item.deviceName || '-'}</td>
                <td>${item.toolNumber || '-'}</td>
                <td>${item.useCount || 0}</td>
                <td>${item.programName || '-'}</td>
                <td>${item.useTime || 0}</td>
            </tr>
        `;
    });

    tableHTML += `
                </tbody>
            </table>
        </div>
        <div class="table-info">
            <span>共找到 ${data.length} 条记录</span>
        </div>
    `;

    resultDiv.innerHTML = tableHTML;
}

// 导出刀具数据
async function exportToolUseData() {
    try {
        const lineId = document.getElementById('tool-line-select').value;
        const deviceId = document.getElementById('tool-device-select').value;
        const programName = document.getElementById('tool-program-name').value;

        const params = new URLSearchParams();
        if (deviceId) params.append('devno', deviceId);
        if (programName) params.append('program_name', programName);

        // 直接下载文件
        window.location.href = `/api/admin/export/tool-use-records?${params}`;
        
        showMessage('开始下载刀具使用数据...', 'success');
    } catch (error) {
        console.error('导出刀具数据错误:', error);
        showMessage('导出失败: ' + error.message, 'error');
    }
}

// 清空刀具筛选条件
function clearToolFilters() {
    document.getElementById('tool-line-select').value = '';
    document.getElementById('tool-device-select').value = '';
    document.getElementById('tool-program-name').value = '';
    
    // 重新加载所有设备
    loadDevices('tool');
    
    // 清空结果
    const resultDiv = document.getElementById('tool-use-result');
    if (resultDiv) {
        resultDiv.innerHTML = '<div class="no-data">请设置筛选条件后点击"加载数据"</div>';
    }
    

}

// 加载刀具页面的生产线列表
async function loadToolProductionLines() {
    try {
        const response = await fetch('/api/admin/production-lines');
        const result = await response.json();

        if (result.success) {
            const toolLineSelect = document.getElementById('tool-line-select');
            
            if (toolLineSelect) {
                // 清空现有选项（保留"全部生产线"选项）
                toolLineSelect.innerHTML = '<option value="">全部生产线</option>';
                
                result.lines.forEach(line => {
                    const option = document.createElement('option');
                    option.value = line.lineid;
                    option.textContent = line.linename;
                    toolLineSelect.appendChild(option);
                });

                // 添加生产线变化事件监听
                toolLineSelect.addEventListener('change', function() {
                    loadToolDevices(this.value);
                });
            }
        }
    } catch (error) {
        console.error('加载刀具页面生产线列表失败:', error);
    }
}

// 加载刀具页面的设备列表
async function loadToolDevices(lineId = '') {
    try {
        const url = lineId ? `/api/admin/devices?line_id=${lineId}` : '/api/admin/devices';
        const response = await fetch(url);
        const result = await response.json();

        if (result.success) {
            const toolDeviceSelect = document.getElementById('tool-device-select');
            
            if (toolDeviceSelect) {
                // 清空现有选项（保留"全部设备"选项）
                toolDeviceSelect.innerHTML = '<option value="">全部设备</option>';

                result.devices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.devno;
                    option.textContent = `${device.devno} - ${device.devname}`;
                    toolDeviceSelect.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('加载刀具页面设备列表失败:', error);
    }
}

// ==================== 订单管理功能 ====================

// 初始化订单录入表单
function initOrderEntryForm() {
    const form = document.getElementById('order-entry-form');
    if (!form) return;

    // 设置默认交货期（30天后）
    const deliveryDateInput = document.getElementById('delivery-date');
    if (deliveryDateInput) {
        const defaultDate = new Date();
        defaultDate.setDate(defaultDate.getDate() + 30);
        deliveryDateInput.value = defaultDate.toISOString().split('T')[0];
    }

    // 添加数量和单价变化监听，自动计算总金额
    const quantityInput = document.getElementById('quantity');
    const unitPriceInput = document.getElementById('unit-price');
    const totalAmountInput = document.getElementById('total-amount');

    function calculateTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitPrice = parseFloat(unitPriceInput.value) || 0;
        const total = (quantity * unitPrice).toFixed(2);
        totalAmountInput.value = total;
    }

    if (quantityInput) quantityInput.addEventListener('input', calculateTotal);
    if (unitPriceInput) unitPriceInput.addEventListener('input', calculateTotal);

    // 添加表单提交监听
    form.addEventListener('submit', handleOrderSubmit);
}



// 处理订单提交
async function handleOrderSubmit(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const orderData = Object.fromEntries(formData.entries());

    // 验证必填字段
    const requiredFields = ['order_number', 'product_name', 'customer_name', 'delivery_date', 'quantity'];
    for (const field of requiredFields) {
        if (!orderData[field]) {
            showMessage(`请填写${getFieldLabel(field)}`, 'error');
            return;
        }
    }

    try {
        const response = await fetch('/api/admin/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderData)
        });

        const result = await response.json();

        if (result.success) {
            showMessage('订单保存成功！', 'success');
            form.reset();

            // 重新设置默认交货期
            const deliveryDateInput = document.getElementById('delivery-date');
            if (deliveryDateInput) {
                const defaultDate = new Date();
                defaultDate.setDate(defaultDate.getDate() + 30);
                deliveryDateInput.value = defaultDate.toISOString().split('T')[0];
            }
        } else {
            showMessage('保存失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('保存失败: ' + error.message, 'error');
    }
}

// 获取字段标签
function getFieldLabel(fieldName) {
    const labels = {
        'order_number': '订单号',
        'product_name': '产品名称',
        'customer_name': '客户名称',
        'delivery_date': '交货期',
        'quantity': '数量'
    };
    return labels[fieldName] || fieldName;
}

// 初始化订单查询表单
function initOrderQueryForm() {
    // 设置默认查询日期范围（最近30天）
    const startDateInput = document.getElementById('query-start-date');
    const endDateInput = document.getElementById('query-end-date');

    if (startDateInput && endDateInput) {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);

        startDateInput.value = startDate.toISOString().split('T')[0];
        endDateInput.value = endDate.toISOString().split('T')[0];
    }
}

// 查询订单
async function queryOrders() {
    const orderNumber = document.getElementById('query-order-number').value;
    const productName = document.getElementById('query-product-name').value;
    const customerName = document.getElementById('query-customer-name').value;
    const startDate = document.getElementById('query-start-date').value;
    const endDate = document.getElementById('query-end-date').value;
    const status = document.getElementById('query-status').value;

    try {
        const params = new URLSearchParams();
        if (orderNumber) params.append('order_number', orderNumber);
        if (productName) params.append('product_name', productName);
        if (customerName) params.append('customer_name', customerName);
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        if (status) params.append('status', status);

        const response = await fetch(`/api/admin/orders?${params}`);
        const result = await response.json();

        if (result.success) {
            displayOrderQueryResult(result.data);
            if (result.data.length > 0) {
                showMessage(`查询成功，找到 ${result.data.length} 条订单记录`, 'success');
            } else {
                showMessage('查询完成，没有找到符合条件的订单', 'info');
            }
        } else {
            showMessage('查询失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('查询失败: ' + error.message, 'error');
    }
}

// 显示订单查询结果
function displayOrderQueryResult(orders) {
    const resultContainer = document.getElementById('order-query-result');
    const resultCountElement = document.getElementById('result-count');
    const exportBtn = document.getElementById('export-btn');

    if (!resultContainer) return;

    // 更新结果计数
    if (resultCountElement) {
        resultCountElement.textContent = `共找到 ${orders.length} 条订单记录`;
    }

    // 启用/禁用导出按钮
    if (exportBtn) {
        exportBtn.disabled = orders.length === 0;
    }

    if (orders.length === 0) {
        resultContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h4>没有找到符合条件的订单</h4>
                <p>请调整查询条件后重新搜索</p>
            </div>
        `;
        return;
    }

    let html = `
        <table class="modern-data-table">
            <thead>
                <tr>
                    <th><i class="fas fa-hashtag"></i> 订单号</th>
                    <th><i class="fas fa-box"></i> 产品名称</th>
                    <th><i class="fas fa-building"></i> 客户名称</th>
                    <th><i class="fas fa-calendar-alt"></i> 交货期</th>
                    <th><i class="fas fa-sort-numeric-up"></i> 数量</th>
                    <th><i class="fas fa-yen-sign"></i> 总金额</th>
                    <th><i class="fas fa-clock"></i> 创建时间</th>
                </tr>
            </thead>
            <tbody>
    `;

    orders.forEach(order => {
        const priorityIcon = getPriorityIcon(order.Priority);

        html += `
            <tr>
                <td>
                    <div style="display: flex; align-items: center; gap: 6px;">
                        ${priorityIcon}
                        <strong>${order.OrderNumber}</strong>
                    </div>
                </td>
                <td>
                    <div>
                        <div style="font-weight: 500;">${order.ProductName}</div>
                        ${order.ProductModel ? `<small style="color: #6c757d;">${order.ProductModel}</small>` : ''}
                    </div>
                </td>
                <td>
                    <div>
                        <div style="font-weight: 500;">${order.CustomerName}</div>
                        ${order.ContactPerson ? `<small style="color: #6c757d;">联系人: ${order.ContactPerson}</small>` : ''}
                    </div>
                </td>
                <td>${formatDate(order.DeliveryDate)}</td>
                <td><span style="font-weight: 600; color: #007bff;">${order.Quantity}</span></td>
                <td>${order.TotalAmount ? '<span style="font-weight: 600; color: #28a745;">¥' + parseFloat(order.TotalAmount).toFixed(2) + '</span>' : '<span style="color: #6c757d;">-</span>'}</td>
                <td>
                    <div style="font-size: 13px;">
                        <div>${formatDate(order.CreatedAt)}</div>
                        <small style="color: #6c757d;">${formatTime(order.CreatedAt)}</small>
                    </div>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table>';

    resultContainer.innerHTML = html;

    // 保存数据供导出使用
    window.currentOrderData = orders;
}

// 获取订单状态文本
function getOrderStatusText(status) {
    const statusMap = {
        'pending': '待确认',
        'confirmed': '已确认',
        'production': '生产中',
        'completed': '已完成',
        'cancelled': '已取消'
    };
    return statusMap[status] || status;
}

// 获取订单状态样式类
function getOrderStatusClass(status) {
    const classMap = {
        'pending': 'status-pending',
        'confirmed': 'status-confirmed',
        'production': 'status-production',
        'completed': 'status-completed',
        'cancelled': 'status-cancelled'
    };
    return classMap[status] || '';
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 查看订单详情
function viewOrderDetail(orderId) {
    // 这里可以实现订单详情查看功能
    showMessage('订单详情功能开发中...', 'info');
}

// 编辑订单
function editOrder(orderId) {
    // 这里可以实现订单编辑功能
    showMessage('订单编辑功能开发中...', 'info');
}

// 导出订单数据
async function exportOrders() {
    if (!window.currentOrderData || window.currentOrderData.length === 0) {
        showMessage('请先查询订单数据', 'error');
        return;
    }

    try {
        // 准备CSV数据
        const headers = ['订单号', '产品名称', '客户名称', '交货期', '产品型号', '数量', '单价', '总金额', '联系人', '联系电话', '创建时间'];

        const csvRows = [
            headers.join(','),
            ...window.currentOrderData.map(order => [
                cleanCSVField(order.OrderNumber),
                cleanCSVField(order.ProductName),
                cleanCSVField(order.CustomerName),
                cleanCSVField(formatDate(order.DeliveryDate)),
                cleanCSVField(order.ProductModel || ''),
                cleanCSVField(order.Quantity),
                cleanCSVField(order.UnitPrice || ''),
                cleanCSVField(order.TotalAmount || ''),
                cleanCSVField(order.ContactPerson || ''),
                cleanCSVField(order.ContactPhone || ''),
                cleanCSVField(formatDateTime(order.CreatedAt))
            ].join(','))
        ];

        const csvContent = csvRows.join('\n');
        const filename = `订单数据_${new Date().toISOString().split('T')[0]}.csv`;

        downloadFile(csvContent, filename);
        showMessage('订单数据导出成功', 'success');
    } catch (error) {
        showMessage('导出失败: ' + error.message, 'error');
    }
}

// 预览订单
function previewOrder() {
    const form = document.getElementById('order-entry-form');
    if (!form) return;

    const formData = new FormData(form);
    const orderData = Object.fromEntries(formData.entries());

    // 创建预览模态框
    const modal = document.createElement('div');
    modal.className = 'order-preview-modal';
    modal.innerHTML = `
        <div class="order-preview-content">
            <div class="order-preview-header">
                <h3><i class="fas fa-eye"></i> 订单预览</h3>
                <span class="close-preview" onclick="closeOrderPreview()">&times;</span>
            </div>
            <div class="order-preview-body">
                <div class="preview-section">
                    <h4><i class="fas fa-info-circle"></i> 基本信息</h4>
                    <div class="preview-grid">
                        <div class="preview-item">
                            <span class="preview-label">订单号：</span>
                            <span class="preview-value">${orderData.order_number || '-'}</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">产品名称：</span>
                            <span class="preview-value">${orderData.product_name || '-'}</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">产品型号：</span>
                            <span class="preview-value">${orderData.product_model || '-'}</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">客户名称：</span>
                            <span class="preview-value">${orderData.customer_name || '-'}</span>
                        </div>
                    </div>
                </div>

                <div class="preview-section">
                    <h4><i class="fas fa-clipboard-list"></i> 订单详情</h4>
                    <div class="preview-grid">
                        <div class="preview-item">
                            <span class="preview-label">交货期：</span>
                            <span class="preview-value">${orderData.delivery_date ? formatDate(orderData.delivery_date) : '-'}</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">数量：</span>
                            <span class="preview-value">${orderData.quantity || '-'}</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">单价：</span>
                            <span class="preview-value">${orderData.unit_price ? '¥' + parseFloat(orderData.unit_price).toFixed(2) : '-'}</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">总金额：</span>
                            <span class="preview-value">${orderData.total_amount ? '¥' + parseFloat(orderData.total_amount).toFixed(2) : '-'}</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">优先级：</span>
                            <span class="preview-value">${getPriorityText(orderData.priority)}</span>
                        </div>
                    </div>
                </div>

                <div class="preview-section">
                    <h4><i class="fas fa-address-book"></i> 联系信息</h4>
                    <div class="preview-grid">
                        <div class="preview-item">
                            <span class="preview-label">联系人：</span>
                            <span class="preview-value">${orderData.contact_person || '-'}</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">联系电话：</span>
                            <span class="preview-value">${orderData.contact_phone || '-'}</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">联系邮箱：</span>
                            <span class="preview-value">${orderData.contact_email || '-'}</span>
                        </div>
                    </div>
                </div>

                <div class="preview-section">
                    <h4><i class="fas fa-sticky-note"></i> 附加信息</h4>
                    <div class="preview-item full-width">
                        <span class="preview-label">交货地址：</span>
                        <span class="preview-value">${orderData.delivery_address || '-'}</span>
                    </div>
                    <div class="preview-item full-width">
                        <span class="preview-label">特殊要求：</span>
                        <span class="preview-value">${orderData.special_requirements || '-'}</span>
                    </div>
                    <div class="preview-item full-width">
                        <span class="preview-label">备注：</span>
                        <span class="preview-value">${orderData.notes || '-'}</span>
                    </div>
                </div>
            </div>
            <div class="order-preview-footer">
                <button onclick="closeOrderPreview()" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 关闭
                </button>
                <button onclick="confirmAndSubmit()" class="btn btn-primary">
                    <i class="fas fa-check"></i> 确认并保存
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'block';
}

// 关闭订单预览
function closeOrderPreview() {
    const modal = document.querySelector('.order-preview-modal');
    if (modal) {
        modal.remove();
    }
}

// 确认并提交订单
function confirmAndSubmit() {
    closeOrderPreview();
    const form = document.getElementById('order-entry-form');
    if (form) {
        form.dispatchEvent(new Event('submit'));
    }
}

// 获取优先级文本
function getPriorityText(priority) {
    const priorityMap = {
        'low': '🟢 低优先级',
        'normal': '🟡 普通优先级',
        'high': '🟠 高优先级',
        'urgent': '🔴 紧急优先级'
    };
    return priorityMap[priority] || priority;
}

// 获取优先级图标
function getPriorityIcon(priority) {
    const iconMap = {
        'low': '<i class="fas fa-circle" style="color: #28a745; font-size: 8px;" title="低优先级"></i>',
        'normal': '<i class="fas fa-circle" style="color: #ffc107; font-size: 8px;" title="普通优先级"></i>',
        'high': '<i class="fas fa-circle" style="color: #fd7e14; font-size: 8px;" title="高优先级"></i>',
        'urgent': '<i class="fas fa-circle" style="color: #dc3545; font-size: 8px;" title="紧急优先级"></i>'
    };
    return iconMap[priority] || iconMap['normal'];
}

// 格式化时间（只显示时分）
function formatTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

// 删除订单
function deleteOrder(orderId) {
    if (!confirm('确定要删除这个订单吗？此操作不可恢复。')) {
        return;
    }

    // 这里可以实现订单删除功能
    showMessage('订单删除功能开发中...', 'info');
}

// 清空订单查询条件
function clearOrderQuery() {
    document.getElementById('query-order-number').value = '';
    document.getElementById('query-product-name').value = '';
    document.getElementById('query-customer-name').value = '';
    document.getElementById('query-start-date').value = '';
    document.getElementById('query-end-date').value = '';
    document.getElementById('query-status').value = '';

    // 重置查询结果显示
    const resultContainer = document.getElementById('order-query-result');
    const resultCountElement = document.getElementById('result-count');
    const exportBtn = document.getElementById('export-btn');

    if (resultContainer) {
        resultContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h4>暂无查询结果</h4>
                <p>请设置查询条件后点击"查询订单"按钮</p>
            </div>
        `;
    }

    if (resultCountElement) {
        resultCountElement.textContent = '请输入查询条件';
    }

    if (exportBtn) {
        exportBtn.disabled = true;
    }

    // 清空导出数据
    window.currentOrderData = null;

    showMessage('查询条件已清空', 'success');
}

// ==================== 故障信息管理功能 ====================

// 加载故障信息列表
async function loadAlarmInfo() {
    try {
        const response = await fetch('/api/admin/alarm-info');
        const data = await response.json();

        if (data.success) {
            displayAlarmInfo(data.alarms);
        } else {
            showMessage(data.message || '加载故障信息失败', 'error');
        }
    } catch (error) {
        console.error('加载故障信息失败:', error);
        showMessage('加载故障信息失败', 'error');
    }
}

// 显示故障信息列表
function displayAlarmInfo(alarms) {
    const tbody = document.querySelector('#alarm-info-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (alarms.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" style="text-align: center; padding: 40px; color: #999;">
                    暂无故障信息数据
                </td>
            </tr>
        `;
        return;
    }

    alarms.forEach(alarm => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${alarm.alarmcode}</td>
            <td>${alarm.alarmremark || '-'}</td>
            <td>${alarm.pushperson || '-'}</td>
            <td>
                <button onclick="editAlarmInfo('${alarm.alarmcode}')" class="btn btn-primary btn-small">编辑</button>
                <button onclick="deleteAlarmInfo('${alarm.alarmcode}')" class="btn btn-danger btn-small">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 显示添加故障信息模态框
function showAddAlarmModal() {
    currentEditingAlarm = null;
    document.getElementById('alarm-modal-title').textContent = '添加故障信息';
    document.getElementById('alarm-code').disabled = false;
    document.getElementById('alarm-info-form').reset();

    const modal = document.getElementById('alarm-info-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

// 编辑故障信息
async function editAlarmInfo(alarmCode) {
    try {
        // 从当前表格中获取数据
        const rows = document.querySelectorAll('#alarm-info-table tbody tr');
        let alarmData = null;

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 3 && cells[0].textContent === alarmCode) {
                alarmData = {
                    alarmcode: cells[0].textContent,
                    alarmremark: cells[1].textContent === '-' ? '' : cells[1].textContent,
                    pushperson: cells[2].textContent === '-' ? '' : cells[2].textContent
                };
            }
        });

        if (alarmData) {
            currentEditingAlarm = alarmCode;
            document.getElementById('alarm-modal-title').textContent = '编辑故障信息';
            document.getElementById('alarm-code').value = alarmData.alarmcode;
            document.getElementById('alarm-code').disabled = true;
            document.getElementById('alarm-remark').value = alarmData.alarmremark;
            document.getElementById('push-person').value = alarmData.pushperson;

            const modal = document.getElementById('alarm-info-modal');
            if (modal) {
                modal.style.display = 'block';
            }
        }
    } catch (error) {
        console.error('编辑故障信息失败:', error);
        showMessage('编辑故障信息失败', 'error');
    }
}

// 删除故障信息
async function deleteAlarmInfo(alarmCode) {
    if (!confirm(`确定要删除故障代码 "${alarmCode}" 吗？`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/alarm-info/${encodeURIComponent(alarmCode)}`, {
            method: 'DELETE'
        });
        const data = await response.json();

        if (data.success) {
            showMessage('故障信息删除成功', 'success');
            loadAlarmInfo(); // 重新加载列表
        } else {
            showMessage(data.message || '删除故障信息失败', 'error');
        }
    } catch (error) {
        console.error('删除故障信息失败:', error);
        showMessage('删除故障信息失败', 'error');
    }
}

// 关闭故障信息模态框
function closeAlarmModal() {
    const modal = document.getElementById('alarm-info-modal');
    if (modal) {
        modal.style.display = 'none';
    }
    currentEditingAlarm = null;
}

// 初始化故障信息表单事件监听器
function initializeAlarmFormListeners() {
    const alarmForm = document.getElementById('alarm-info-form');
    if (alarmForm) {
        alarmForm.addEventListener('submit', handleAlarmFormSubmit);
    }

    // 模态框点击外部关闭
    const modal = document.getElementById('alarm-info-modal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeAlarmModal();
            }
        });
    }
}

// 处理故障信息表单提交
async function handleAlarmFormSubmit(e) {
    e.preventDefault();

    const alarmCode = document.getElementById('alarm-code').value.trim();
    const alarmRemark = document.getElementById('alarm-remark').value.trim();
    const pushPersonRaw = document.getElementById('push-person').value.trim();

    if (!alarmCode) {
        showMessage('故障代码不能为空', 'error');
        return;
    }

    // 格式化和验证手机号（如果有输入）
    let pushPerson = '';
    if (pushPersonRaw) {
        if (!validatePhoneNumbers(pushPersonRaw)) {
            showMessage('手机号格式不正确，请输入正确的11位手机号', 'error');
            return;
        }
        pushPerson = formatPhoneNumbers(pushPersonRaw);
    }

    const formData = {
        alarmcode: alarmCode,
        alarmremark: alarmRemark,
        pushperson: pushPerson
    };

    try {
        let response;
        if (currentEditingAlarm) {
            // 编辑模式
            response = await fetch(`/api/admin/alarm-info/${encodeURIComponent(currentEditingAlarm)}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        } else {
            // 添加模式
            response = await fetch('/api/admin/alarm-info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        }

        const data = await response.json();

        if (data.success) {
            showMessage(data.message || '操作成功', 'success');
            closeAlarmModal();
            loadAlarmInfo(); // 重新加载列表
        } else {
            showMessage(data.message || '操作失败', 'error');
        }
    } catch (error) {
        console.error('提交故障信息失败:', error);
        showMessage('提交故障信息失败', 'error');
    }
}

// 验证手机号格式 - 支持分号和换行符分隔
function validatePhoneNumbers(phoneStr) {
    if (!phoneStr) return true;

    // 支持分号和换行符分隔
    const phones = phoneStr.split(/[;\n\r]+/);
    const phoneRegex = /^1[3-9]\d{9}$/;

    for (let phone of phones) {
        phone = phone.trim();
        if (phone && !phoneRegex.test(phone)) {
            return false;
        }
    }
    return true;
}

// 格式化手机号字符串 - 统一使用分号分隔
function formatPhoneNumbers(phoneStr) {
    if (!phoneStr) return '';

    // 将换行符替换为分号，然后清理多余的分号
    return phoneStr
        .replace(/[\n\r]+/g, ';')
        .split(';')
        .map(phone => phone.trim())
        .filter(phone => phone)
        .join(';');
}
